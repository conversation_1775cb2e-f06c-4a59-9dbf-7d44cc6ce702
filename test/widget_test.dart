// This is a basic Flutter widget test.
//
// To perform an interaction with a widget in your test, use the WidgetTester
// utility in the flutter_test package. For example, you can send tap and scroll
// gestures. You can also use WidgetTester to find child widgets in the widget
// tree, read text, and verify that the values of widget properties are correct.

import 'package:flutter_test/flutter_test.dart';

import 'package:bulk_json_translator/main.dart';

void main() {
  testWidgets('App loads configuration screen', (WidgetTester tester) async {
    // Build our app and trigger a frame.
    await tester.pumpWidget(const BulkJsonTranslatorApp());

    // Verify that the configuration screen loads
    expect(find.text('Bulk JSON Translator'), findsOneWidget);
    expect(find.text('Configuration'), findsOneWidget);
    expect(find.text('Base Language'), findsOneWidget);
    expect(find.text('Target Languages'), findsOneWidget);
    expect(find.text('OpenAI API Key'), findsOneWidget);
  });
}
