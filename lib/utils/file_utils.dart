import 'dart:convert';
import 'dart:typed_data';
import 'package:archive/archive.dart';
import 'package:file_picker/file_picker.dart';
import '../models/translation_data.dart';
import 'locale_utils.dart';

class FileUtils {
  static Future<Map<String, dynamic>?> pickAndParseJsonFile() async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['json'],
        allowMultiple: false,
        withData: true,
      );

      if (result != null && result.files.isNotEmpty) {
        final file = result.files.first;
        if (file.bytes != null) {
          final content = utf8.decode(file.bytes!);
          final jsonData = json.decode(content) as Map<String, dynamic>;
          return jsonData;
        }
      }
      return null;
    } catch (e) {
      throw Exception('Failed to parse JSON file: ${e.toString()}');
    }
  }

  static Uint8List createTranslationZip(
    List<TranslationResult> results,
    Map<String, dynamic> baseJson,
    String baseLocale,
  ) {
    final archive = Archive();

    // Add base locale file
    final baseFileName = LocaleUtils.getFileNameFromLocale(baseLocale);
    final baseContent = const JsonEncoder.withIndent('  ').convert(baseJson);
    final baseFile = ArchiveFile(
      baseFileName,
      baseContent.length,
      utf8.encode(baseContent),
    );
    archive.addFile(baseFile);

    // Add translated files
    for (final result in results) {
      if (result.status == TranslationStatus.completed && 
          result.translatedJson.isNotEmpty) {
        final fileName = LocaleUtils.getFileNameFromLocale(result.locale);
        final content = const JsonEncoder.withIndent('  ').convert(result.translatedJson);
        final file = ArchiveFile(
          fileName,
          content.length,
          utf8.encode(content),
        );
        archive.addFile(file);
      }
    }

    // Create zip
    final zipEncoder = ZipEncoder();
    final zipData = zipEncoder.encode(archive);
    return Uint8List.fromList(zipData!);
  }

  static void downloadFile(Uint8List bytes, String fileName) {
    // For web, we'll use the browser's download functionality
    // This will be implemented in the UI layer using html package or similar
    // For now, this is a placeholder that will be called from the UI
  }

  static bool isValidJson(String content) {
    try {
      json.decode(content);
      return true;
    } catch (e) {
      return false;
    }
  }

  static String formatFileSize(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    }
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }

  static Map<String, dynamic> flattenJson(Map<String, dynamic> json, [String prefix = '']) {
    final Map<String, dynamic> flattened = {};
    
    json.forEach((key, value) {
      final newKey = prefix.isEmpty ? key : '$prefix.$key';
      
      if (value is Map<String, dynamic>) {
        flattened.addAll(flattenJson(value, newKey));
      } else {
        flattened[newKey] = value;
      }
    });
    
    return flattened;
  }

  static Map<String, dynamic> unflattenJson(Map<String, dynamic> flattened) {
    final Map<String, dynamic> unflattened = {};
    
    flattened.forEach((key, value) {
      final keys = key.split('.');
      Map<String, dynamic> current = unflattened;
      
      for (int i = 0; i < keys.length - 1; i++) {
        current[keys[i]] ??= <String, dynamic>{};
        current = current[keys[i]] as Map<String, dynamic>;
      }
      
      current[keys.last] = value;
    });
    
    return unflattened;
  }
}
