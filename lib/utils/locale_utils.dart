import '../models/translation_data.dart';

class LocaleUtils {
  static const List<LocaleInfo> supportedLocales = [
    LocaleInfo(code: 'en-US', name: 'English (US)', flag: '🇺🇸'),
    LocaleInfo(code: 'en-GB', name: 'English (UK)', flag: '🇬🇧'),
    LocaleInfo(code: 'es-ES', name: 'Spanish (Spain)', flag: '🇪🇸'),
    LocaleInfo(code: 'es-MX', name: 'Spanish (Mexico)', flag: '🇲🇽'),
    LocaleInfo(code: 'fr-FR', name: 'French (France)', flag: '🇫🇷'),
    LocaleInfo(code: 'fr-CA', name: 'French (Canada)', flag: '🇨🇦'),
    LocaleInfo(code: 'de-DE', name: 'German (Germany)', flag: '🇩🇪'),
    LocaleInfo(code: 'it-IT', name: 'Italian (Italy)', flag: '🇮🇹'),
    LocaleInfo(code: 'pt-BR', name: 'Portuguese (Brazil)', flag: '🇧🇷'),
    LocaleInfo(code: 'pt-PT', name: 'Portuguese (Portugal)', flag: '🇵🇹'),
    LocaleInfo(code: 'ru-RU', name: 'Russian (Russia)', flag: '🇷🇺'),
    LocaleInfo(code: 'zh-CN', name: 'Chinese (Simplified)', flag: '🇨🇳'),
    LocaleInfo(code: 'zh-TW', name: 'Chinese (Traditional)', flag: '🇹🇼'),
    LocaleInfo(code: 'ja-JP', name: 'Japanese (Japan)', flag: '🇯🇵'),
    LocaleInfo(code: 'ko-KR', name: 'Korean (South Korea)', flag: '🇰🇷'),
    LocaleInfo(code: 'ar-SA', name: 'Arabic (Saudi Arabia)', flag: '🇸🇦'),
    LocaleInfo(code: 'hi-IN', name: 'Hindi (India)', flag: '🇮🇳'),
    LocaleInfo(code: 'th-TH', name: 'Thai (Thailand)', flag: '🇹🇭'),
    LocaleInfo(code: 'vi-VN', name: 'Vietnamese (Vietnam)', flag: '🇻🇳'),
    LocaleInfo(code: 'tr-TR', name: 'Turkish (Turkey)', flag: '🇹🇷'),
    LocaleInfo(code: 'pl-PL', name: 'Polish (Poland)', flag: '🇵🇱'),
    LocaleInfo(code: 'nl-NL', name: 'Dutch (Netherlands)', flag: '🇳🇱'),
    LocaleInfo(code: 'sv-SE', name: 'Swedish (Sweden)', flag: '🇸🇪'),
    LocaleInfo(code: 'da-DK', name: 'Danish (Denmark)', flag: '🇩🇰'),
    LocaleInfo(code: 'no-NO', name: 'Norwegian (Norway)', flag: '🇳🇴'),
    LocaleInfo(code: 'fi-FI', name: 'Finnish (Finland)', flag: '🇫🇮'),
    LocaleInfo(code: 'he-IL', name: 'Hebrew (Israel)', flag: '🇮🇱'),
    LocaleInfo(code: 'cs-CZ', name: 'Czech (Czech Republic)', flag: '🇨🇿'),
    LocaleInfo(code: 'sk-SK', name: 'Slovak (Slovakia)', flag: '🇸🇰'),
    LocaleInfo(code: 'hu-HU', name: 'Hungarian (Hungary)', flag: '🇭🇺'),
    LocaleInfo(code: 'ro-RO', name: 'Romanian (Romania)', flag: '🇷🇴'),
    LocaleInfo(code: 'bg-BG', name: 'Bulgarian (Bulgaria)', flag: '🇧🇬'),
    LocaleInfo(code: 'hr-HR', name: 'Croatian (Croatia)', flag: '🇭🇷'),
    LocaleInfo(code: 'sr-RS', name: 'Serbian (Serbia)', flag: '🇷🇸'),
    LocaleInfo(code: 'sl-SI', name: 'Slovenian (Slovenia)', flag: '🇸🇮'),
    LocaleInfo(code: 'et-EE', name: 'Estonian (Estonia)', flag: '🇪🇪'),
    LocaleInfo(code: 'lv-LV', name: 'Latvian (Latvia)', flag: '🇱🇻'),
    LocaleInfo(code: 'lt-LT', name: 'Lithuanian (Lithuania)', flag: '🇱🇹'),
    LocaleInfo(code: 'uk-UA', name: 'Ukrainian (Ukraine)', flag: '🇺🇦'),
    LocaleInfo(code: 'be-BY', name: 'Belarusian (Belarus)', flag: '🇧🇾'),
  ];

  static LocaleInfo? getLocaleInfo(String code) {
    try {
      return supportedLocales.firstWhere((locale) => locale.code == code);
    } catch (e) {
      return null;
    }
  }

  static String getDisplayName(String code) {
    final locale = getLocaleInfo(code);
    return locale?.name ?? code;
  }

  static String getFlag(String code) {
    final locale = getLocaleInfo(code);
    return locale?.flag ?? '🌐';
  }

  static String getFileNameFromLocale(String localeCode) {
    // Convert locale code to standard file naming convention
    // e.g., 'en-US' -> 'en_US.json' or 'en.json'
    if (localeCode.contains('-')) {
      final parts = localeCode.split('-');
      if (parts.length == 2) {
        final language = parts[0].toLowerCase();
        final country = parts[1].toUpperCase();
        return '${language}_$country.json';
      }
    }
    return '${localeCode.toLowerCase()}.json';
  }

  static List<String> validateKeys(
    Map<String, dynamic> baseJson,
    Map<String, dynamic> translatedJson,
  ) {
    final missingKeys = <String>[];
    
    void checkKeys(Map<String, dynamic> base, Map<String, dynamic> translated, String prefix) {
      for (final key in base.keys) {
        final currentKey = prefix.isEmpty ? key : '$prefix.$key';
        
        if (!translated.containsKey(key)) {
          missingKeys.add(currentKey);
        } else if (base[key] is Map<String, dynamic> && translated[key] is Map<String, dynamic>) {
          checkKeys(
            base[key] as Map<String, dynamic>,
            translated[key] as Map<String, dynamic>,
            currentKey,
          );
        }
      }
    }
    
    checkKeys(baseJson, translatedJson, '');
    return missingKeys;
  }
}
