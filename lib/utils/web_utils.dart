import 'package:flutter/foundation.dart';

// Conditional import for web
import 'web_utils_stub.dart'
    if (dart.library.html) 'web_utils_web.dart';

void downloadFile(Uint8List bytes, String fileName) {
  if (kIsWeb) {
    downloadFileWeb(bytes, fileName);
  } else {
    // For non-web platforms, you could implement file saving here
    // For now, we'll just show a message
    throw UnsupportedError('File download is only supported on web');
  }
}
