import 'package:flutter/material.dart';
import '../models/translation_data.dart';
import '../utils/locale_utils.dart';

class LanguageSelector extends StatelessWidget {
  final String? selectedLocale;
  final Function(String?) onChanged;
  final String label;
  final String? errorMessage;

  const LanguageSelector({
    super.key,
    required this.selectedLocale,
    required this.onChanged,
    required this.label,
    this.errorMessage,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 8),
        DropdownButtonFormField<String>(
          value: selectedLocale,
          decoration: InputDecoration(
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: Colors.red, width: 2),
            ),
            contentPadding: const EdgeInsets.symmetric(
              horizontal: 12,
              vertical: 16,
            ),
            errorText: errorMessage,
          ),
          hint: const Text('Select a language'),
          isExpanded: true,
          items: LocaleUtils.supportedLocales.map((locale) {
            return DropdownMenuItem<String>(
              value: locale.code,
              child: Row(
                children: [
                  Text(
                    locale.flag,
                    style: const TextStyle(fontSize: 20),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      locale.name,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
            );
          }).toList(),
          onChanged: onChanged,
        ),
      ],
    );
  }
}

class MultiLanguageSelector extends StatefulWidget {
  final List<String> selectedLocales;
  final Function(List<String>) onChanged;
  final String label;
  final String? errorMessage;
  final String? excludeLocale;

  const MultiLanguageSelector({
    super.key,
    required this.selectedLocales,
    required this.onChanged,
    required this.label,
    this.errorMessage,
    this.excludeLocale,
  });

  @override
  State<MultiLanguageSelector> createState() => _MultiLanguageSelectorState();
}

class _MultiLanguageSelectorState extends State<MultiLanguageSelector> {
  bool _isExpanded = false;

  List<LocaleInfo> get _availableLocales {
    return LocaleUtils.supportedLocales
        .where((locale) => locale.code != widget.excludeLocale)
        .toList();
  }

  void _toggleLocale(String localeCode) {
    final newSelection = List<String>.from(widget.selectedLocales);
    if (newSelection.contains(localeCode)) {
      newSelection.remove(localeCode);
    } else {
      newSelection.add(localeCode);
    }
    widget.onChanged(newSelection);
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          widget.label,
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          decoration: BoxDecoration(
            border: Border.all(
              color: widget.errorMessage != null 
                  ? Colors.red 
                  : Colors.grey.shade300,
              width: widget.errorMessage != null ? 2 : 1,
            ),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Column(
            children: [
              InkWell(
                onTap: () {
                  setState(() {
                    _isExpanded = !_isExpanded;
                  });
                },
                borderRadius: BorderRadius.circular(8),
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 16,
                  ),
                  child: Row(
                    children: [
                      Expanded(
                        child: widget.selectedLocales.isEmpty
                            ? Text(
                                'Select target languages',
                                style: TextStyle(
                                  color: Colors.grey.shade600,
                                ),
                              )
                            : Wrap(
                                spacing: 8,
                                runSpacing: 4,
                                children: widget.selectedLocales.map((code) {
                                  final locale = LocaleUtils.getLocaleInfo(code);
                                  return Chip(
                                    label: Text(
                                      '${locale?.flag ?? '🌐'} ${locale?.name ?? code}',
                                      style: const TextStyle(fontSize: 12),
                                    ),
                                    onDeleted: () => _toggleLocale(code),
                                    deleteIconColor: Colors.grey.shade600,
                                    materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                                  );
                                }).toList(),
                              ),
                      ),
                      Icon(
                        _isExpanded 
                            ? Icons.keyboard_arrow_up 
                            : Icons.keyboard_arrow_down,
                        color: Colors.grey.shade600,
                      ),
                    ],
                  ),
                ),
              ),
              if (_isExpanded) ...[
                const Divider(height: 1),
                Container(
                  constraints: const BoxConstraints(maxHeight: 200),
                  child: ListView.builder(
                    shrinkWrap: true,
                    itemCount: _availableLocales.length,
                    itemBuilder: (context, index) {
                      final locale = _availableLocales[index];
                      final isSelected = widget.selectedLocales.contains(locale.code);
                      
                      return CheckboxListTile(
                        value: isSelected,
                        onChanged: (_) => _toggleLocale(locale.code),
                        title: Row(
                          children: [
                            Text(
                              locale.flag,
                              style: const TextStyle(fontSize: 20),
                            ),
                            const SizedBox(width: 12),
                            Expanded(
                              child: Text(
                                locale.name,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          ],
                        ),
                        dense: true,
                        controlAffinity: ListTileControlAffinity.leading,
                      );
                    },
                  ),
                ),
              ],
            ],
          ),
        ),
        if (widget.errorMessage != null) ...[
          const SizedBox(height: 8),
          Text(
            widget.errorMessage!,
            style: TextStyle(
              color: Colors.red.shade700,
              fontSize: 12,
            ),
          ),
        ],
      ],
    );
  }
}
