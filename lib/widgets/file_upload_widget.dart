import 'package:flutter/material.dart';
import '../utils/file_utils.dart';

class FileUploadWidget extends StatefulWidget {
  final Function(Map<String, dynamic>) onFileSelected;
  final String? errorMessage;

  const FileUploadWidget({
    super.key,
    required this.onFileSelected,
    this.errorMessage,
  });

  @override
  State<FileUploadWidget> createState() => _FileUploadWidgetState();
}

class _FileUploadWidgetState extends State<FileUploadWidget> {
  bool _isLoading = false;
  String? _fileName;
  Map<String, dynamic>? _jsonData;

  Future<void> _pickFile() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final jsonData = await FileUtils.pickAndParseJsonFile();
      if (jsonData != null) {
        setState(() {
          _jsonData = jsonData;
          _fileName = 'localization.json'; // Default name since we can't get actual filename in web
        });
        widget.onFileSelected(jsonData);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading file: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _clearFile() {
    setState(() {
      _fileName = null;
      _jsonData = null;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Base JSON Localization File',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          width: double.infinity,
          decoration: BoxDecoration(
            border: Border.all(
              color: widget.errorMessage != null 
                  ? Colors.red 
                  : Colors.grey.shade300,
              width: 2,
            ),
            borderRadius: BorderRadius.circular(8),
          ),
          child: _jsonData == null
              ? _buildUploadArea()
              : _buildFileInfo(),
        ),
        if (widget.errorMessage != null) ...[
          const SizedBox(height: 8),
          Text(
            widget.errorMessage!,
            style: TextStyle(
              color: Colors.red.shade700,
              fontSize: 12,
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildUploadArea() {
    return InkWell(
      onTap: _isLoading ? null : _pickFile,
      borderRadius: BorderRadius.circular(6),
      child: Container(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            if (_isLoading)
              const CircularProgressIndicator()
            else ...[
              Icon(
                Icons.cloud_upload_outlined,
                size: 48,
                color: Colors.grey.shade600,
              ),
              const SizedBox(height: 16),
              Text(
                'Click to upload JSON file',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: Colors.grey.shade700,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'Supports .json files only',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey.shade500,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildFileInfo() {
    final keyCount = _countKeys(_jsonData!);
    
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.green.shade100,
              borderRadius: BorderRadius.circular(6),
            ),
            child: Icon(
              Icons.description,
              color: Colors.green.shade700,
              size: 24,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  _fileName ?? 'localization.json',
                  style: const TextStyle(
                    fontWeight: FontWeight.w500,
                    fontSize: 14,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  '$keyCount translation keys found',
                  style: TextStyle(
                    color: Colors.grey.shade600,
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),
          IconButton(
            onPressed: _clearFile,
            icon: Icon(
              Icons.close,
              color: Colors.grey.shade600,
            ),
            tooltip: 'Remove file',
          ),
        ],
      ),
    );
  }

  int _countKeys(Map<String, dynamic> json) {
    int count = 0;
    
    void countRecursive(Map<String, dynamic> map) {
      for (final value in map.values) {
        if (value is Map<String, dynamic>) {
          countRecursive(value);
        } else {
          count++;
        }
      }
    }
    
    countRecursive(json);
    return count;
  }
}
