import 'package:flutter/material.dart';
import 'package:flutter_json_view/flutter_json_view.dart';
import '../models/translation_data.dart';
import '../utils/locale_utils.dart';

class TranslationTab extends StatelessWidget {
  final TranslationResult result;
  final VoidCallback? onRegenerate;
  final bool isActive;

  const TranslationTab({
    super.key,
    required this.result,
    this.onRegenerate,
    this.isActive = false,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeader(context),
          const SizedBox(height: 16),
          Expanded(
            child: _buildContent(context),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    final locale = LocaleUtils.getLocaleInfo(result.locale);
    
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          decoration: BoxDecoration(
            color: _getStatusColor().withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(16),
            border: Border.all(color: _getStatusColor()),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                _getStatusIcon(),
                size: 16,
                color: _getStatusColor(),
              ),
              const SizedBox(width: 6),
              Text(
                _getStatusText(),
                style: TextStyle(
                  color: _getStatusColor(),
                  fontWeight: FontWeight.w600,
                  fontSize: 12,
                ),
              ),
            ],
          ),
        ),
        const SizedBox(width: 12),
        Text(
          '${locale?.flag ?? '🌐'} ${locale?.name ?? result.locale}',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const Spacer(),
        if (result.status == TranslationStatus.error && onRegenerate != null)
          ElevatedButton.icon(
            onPressed: onRegenerate,
            icon: const Icon(Icons.refresh, size: 16),
            label: const Text('Regenerate'),
            style: ElevatedButton.styleFrom(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            ),
          ),
      ],
    );
  }

  Widget _buildContent(BuildContext context) {
    switch (result.status) {
      case TranslationStatus.waiting:
        return _buildWaitingState();
      case TranslationStatus.processing:
        return _buildProcessingState();
      case TranslationStatus.completed:
        return _buildCompletedState(context);
      case TranslationStatus.error:
        return _buildErrorState(context);
    }
  }

  Widget _buildWaitingState() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.schedule,
            size: 48,
            color: Colors.grey,
          ),
          SizedBox(height: 16),
          Text(
            'Waiting to start translation...',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProcessingState() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(),
          SizedBox(height: 16),
          Text(
            'Translating...',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
          ),
          SizedBox(height: 8),
          Text(
            'This may take a few moments',
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCompletedState(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (result.missingKeys.isNotEmpty) ...[
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.orange.shade50,
              border: Border.all(color: Colors.orange.shade200),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.warning,
                  color: Colors.orange.shade700,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Missing Keys Warning',
                        style: TextStyle(
                          fontWeight: FontWeight.w600,
                          color: Colors.orange.shade700,
                        ),
                      ),
                      Text(
                        '${result.missingKeys.length} keys were not translated: ${result.missingKeys.take(3).join(", ")}${result.missingKeys.length > 3 ? "..." : ""}',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.orange.shade600,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 16),
        ],
        Expanded(
          child: Container(
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey.shade300),
              borderRadius: BorderRadius.circular(8),
            ),
            child: JsonView.map(
              result.translatedJson,
              theme: JsonViewTheme(
                backgroundColor: Colors.white,
                defaultTextStyle: const TextStyle(fontSize: 12),
                keyStyle: const TextStyle(
                  color: Colors.blue,
                  fontWeight: FontWeight.w600,
                ),
                stringStyle: const TextStyle(color: Colors.green),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildErrorState(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 48,
            color: Colors.red.shade400,
          ),
          const SizedBox(height: 16),
          Text(
            'Translation Failed',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Colors.red.shade700,
            ),
          ),
          const SizedBox(height: 8),
          Container(
            padding: const EdgeInsets.all(16),
            margin: const EdgeInsets.symmetric(horizontal: 32),
            decoration: BoxDecoration(
              color: Colors.red.shade50,
              border: Border.all(color: Colors.red.shade200),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Text(
              result.error ?? 'Unknown error occurred',
              style: TextStyle(
                fontSize: 12,
                color: Colors.red.shade700,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ],
      ),
    );
  }

  Color _getStatusColor() {
    switch (result.status) {
      case TranslationStatus.waiting:
        return Colors.grey;
      case TranslationStatus.processing:
        return Colors.blue;
      case TranslationStatus.completed:
        return Colors.green;
      case TranslationStatus.error:
        return Colors.red;
    }
  }

  IconData _getStatusIcon() {
    switch (result.status) {
      case TranslationStatus.waiting:
        return Icons.schedule;
      case TranslationStatus.processing:
        return Icons.sync;
      case TranslationStatus.completed:
        return Icons.check_circle;
      case TranslationStatus.error:
        return Icons.error;
    }
  }

  String _getStatusText() {
    switch (result.status) {
      case TranslationStatus.waiting:
        return 'Waiting';
      case TranslationStatus.processing:
        return 'Processing';
      case TranslationStatus.completed:
        return 'Completed';
      case TranslationStatus.error:
        return 'Error';
    }
  }
}
