import 'dart:convert';
import 'package:openai_dart/openai_dart.dart';
import '../models/translation_data.dart';
import '../utils/file_utils.dart';
import '../utils/locale_utils.dart';

class TranslationService {
  late final OpenAIClient _client;
  
  TranslationService(String apiKey) {
    _client = OpenAIClient(apiKey: apiKey);
  }

  Future<TranslationResult> translateToLocale({
    required Map<String, dynamic> baseJson,
    required String baseLocale,
    required String targetLocale,
  }) async {
    try {
      // Flatten the JSON for easier translation
      final flattenedJson = FileUtils.flattenJson(baseJson);
      
      // Create translation prompt
      final prompt = _createTranslationPrompt(
        flattenedJson,
        baseLocale,
        targetLocale,
      );

      // Call OpenAI API
      final response = await _client.createChatCompletion(
        request: CreateChatCompletionRequest(
          model: ChatCompletionModel.modelId('gpt-3.5-turbo'),
          messages: [
            ChatCompletionMessage.system(
              content: 'You are a professional translator. Translate the provided JSON localization file while maintaining the exact structure and keys. Only translate the values, never the keys. Preserve any placeholders, variables, or special formatting in the values.',
            ),
            ChatCompletionMessage.user(
              content: ChatCompletionUserMessageContent.string(prompt),
            ),
          ],
          temperature: 0.3,
          maxTokens: 4000,
        ),
      );

      final translatedContent = response.choices.first.message.content;
      if (translatedContent == null) {
        throw Exception('No translation received from OpenAI');
      }

      // Parse the translated JSON
      final translatedFlattened = _parseTranslatedResponse(translatedContent);
      final translatedJson = FileUtils.unflattenJson(translatedFlattened);

      // Validate keys
      final missingKeys = LocaleUtils.validateKeys(baseJson, translatedJson);

      return TranslationResult(
        locale: targetLocale,
        translatedJson: translatedJson,
        status: TranslationStatus.completed,
        missingKeys: missingKeys,
      );
    } catch (e) {
      return TranslationResult(
        locale: targetLocale,
        translatedJson: {},
        status: TranslationStatus.error,
        error: e.toString(),
      );
    }
  }

  String _createTranslationPrompt(
    Map<String, dynamic> flattenedJson,
    String baseLocale,
    String targetLocale,
  ) {
    final baseLocaleName = LocaleUtils.getDisplayName(baseLocale);
    final targetLocaleName = LocaleUtils.getDisplayName(targetLocale);
    
    final jsonString = const JsonEncoder.withIndent('  ').convert(flattenedJson);
    
    return '''
Please translate the following JSON localization file from $baseLocaleName to $targetLocaleName.

IMPORTANT INSTRUCTIONS:
1. Translate ONLY the values, never the keys
2. Maintain the exact JSON structure and formatting
3. Preserve any placeholders like {0}, {1}, {{variable}}, %s, %d, etc.
4. Preserve any HTML tags or special formatting
5. Keep any URLs, email addresses, or technical terms unchanged
6. Ensure cultural appropriateness for the target locale
7. Return ONLY the translated JSON, no additional text or explanations

Source JSON ($baseLocaleName):
$jsonString

Translated JSON ($targetLocaleName):''';
  }

  Map<String, dynamic> _parseTranslatedResponse(String response) {
    try {
      // Try to extract JSON from the response
      final jsonStart = response.indexOf('{');
      final jsonEnd = response.lastIndexOf('}');
      
      if (jsonStart != -1 && jsonEnd != -1 && jsonEnd > jsonStart) {
        final jsonString = response.substring(jsonStart, jsonEnd + 1);
        return json.decode(jsonString) as Map<String, dynamic>;
      }
      
      // If no JSON found, try to parse the entire response
      return json.decode(response) as Map<String, dynamic>;
    } catch (e) {
      throw Exception('Failed to parse translated JSON: ${e.toString()}');
    }
  }

  void dispose() {
    // OpenAI client doesn't need explicit disposal
  }
}
