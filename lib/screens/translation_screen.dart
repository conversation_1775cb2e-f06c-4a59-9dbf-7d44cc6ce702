import 'package:flutter/material.dart';
import 'package:flutter_json_view/flutter_json_view.dart';
import 'package:provider/provider.dart';
import '../models/translation_data.dart';
import '../models/translation_state.dart';
import '../services/translation_service.dart';
import '../widgets/translation_tab.dart';
import '../utils/file_utils.dart';
import '../utils/locale_utils.dart';
import '../utils/web_utils.dart';

class TranslationScreen extends StatefulWidget {
  final TranslationConfig config;

  const TranslationScreen({
    super.key,
    required this.config,
  });

  @override
  State<TranslationScreen> createState() => _TranslationScreenState();
}

class _TranslationScreenState extends State<TranslationScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  late TranslationService _translationService;
  late TranslationState _translationState;

  @override
  void initState() {
    super.initState();
    _translationState = TranslationState();
    _translationState.setConfig(widget.config);
    _translationService = TranslationService(widget.config.apiKey);
    
    // Create tab controller with base tab + target locale tabs
    _tabController = TabController(
      length: widget.config.targetLocales.length + 1,
      vsync: this,
    );
    
    // Start translation process
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _startTranslationProcess();
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    _translationService.dispose();
    _translationState.dispose();
    super.dispose();
  }

  Future<void> _startTranslationProcess() async {
    _translationState.startTranslation();
    
    for (int i = 0; i < widget.config.targetLocales.length; i++) {
      final targetLocale = widget.config.targetLocales[i];
      
      // Update status to processing
      _translationState.updateTranslationResult(
        i,
        TranslationResult(
          locale: targetLocale,
          translatedJson: {},
          status: TranslationStatus.processing,
        ),
      );
      
      // Perform translation
      final result = await _translationService.translateToLocale(
        baseJson: widget.config.baseJson,
        baseLocale: widget.config.baseLocale,
        targetLocale: targetLocale,
      );
      
      // Update with result
      _translationState.updateTranslationResult(i, result);
      _translationState.moveToNextTranslation();
      
      // Small delay between translations to avoid rate limiting
      if (i < widget.config.targetLocales.length - 1) {
        await Future.delayed(const Duration(milliseconds: 500));
      }
    }
  }

  Future<void> _retryTranslation(int index) async {
    final targetLocale = widget.config.targetLocales[index];
    
    // Update status to processing
    _translationState.updateTranslationResult(
      index,
      TranslationResult(
        locale: targetLocale,
        translatedJson: {},
        status: TranslationStatus.processing,
      ),
    );
    
    // Perform translation
    final result = await _translationService.translateToLocale(
      baseJson: widget.config.baseJson,
      baseLocale: widget.config.baseLocale,
      targetLocale: targetLocale,
    );
    
    // Update with result
    _translationState.updateTranslationResult(index, result);
  }

  void _downloadAllTranslations() {
    try {
      final zipData = FileUtils.createTranslationZip(
        _translationState.results,
        widget.config.baseJson,
        widget.config.baseLocale,
      );
      
      downloadFile(zipData, 'translations.zip');
      
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Translations downloaded successfully!'),
          backgroundColor: Colors.green,
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error creating download: ${e.toString()}'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }



  Widget _buildTabBar() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.shade200,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: TabBar(
        controller: _tabController,
        isScrollable: true,
        tabs: [
          // Base locale tab
          Tab(
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(LocaleUtils.getFlag(widget.config.baseLocale)),
                const SizedBox(width: 8),
                Text('Base (${LocaleUtils.getDisplayName(widget.config.baseLocale)})'),
              ],
            ),
          ),
          // Target locale tabs
          ...widget.config.targetLocales.asMap().entries.map((entry) {
            final index = entry.key;
            final locale = entry.value;

            return Consumer<TranslationState>(
              builder: (context, state, child) {
                final result = index < state.results.length ? state.results[index] : null;
                final statusIcon = result != null ? _getStatusIcon(result.status) : Icons.schedule;
                final statusColor = result != null ? _getStatusColor(result.status) : Colors.grey;

                return Tab(
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(statusIcon, size: 16, color: statusColor),
                      const SizedBox(width: 4),
                      Text(LocaleUtils.getFlag(locale)),
                      const SizedBox(width: 4),
                      Text(LocaleUtils.getDisplayName(locale).split(' ').first),
                    ],
                  ),
                );
              },
            );
          }),
        ],
      ),
    );
  }

  Widget _buildTabBarView() {
    return TabBarView(
      controller: _tabController,
      children: [
        // Base locale view
        _buildBaseLocaleView(),
        // Target locale views
        ...widget.config.targetLocales.asMap().entries.map((entry) {
          final index = entry.key;
          return Consumer<TranslationState>(
            builder: (context, state, child) {
              final result = index < state.results.length
                  ? state.results[index]
                  : TranslationResult(
                      locale: entry.value,
                      translatedJson: {},
                      status: TranslationStatus.waiting,
                    );

              return TranslationTab(
                result: result,
                onRegenerate: () => _retryTranslation(index),
              );
            },
          );
        }),
      ],
    );
  }

  Widget _buildBaseLocaleView() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: Colors.blue.shade100,
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(color: Colors.blue),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(Icons.source, size: 16, color: Colors.blue.shade700),
                    const SizedBox(width: 6),
                    Text(
                      'Base File',
                      style: TextStyle(
                        color: Colors.blue.shade700,
                        fontWeight: FontWeight.w600,
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(width: 12),
              Text(
                '${LocaleUtils.getFlag(widget.config.baseLocale)} ${LocaleUtils.getDisplayName(widget.config.baseLocale)}',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Expanded(
            child: Container(
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey.shade300),
                borderRadius: BorderRadius.circular(8),
              ),
              child: JsonView.map(
                widget.config.baseJson,
                theme: JsonViewTheme(
                  backgroundColor: Colors.white,
                  defaultTextStyle: const TextStyle(fontSize: 12),
                  keyStyle: const TextStyle(
                    color: Colors.blue,
                    fontWeight: FontWeight.w600,
                  ),
                  stringStyle: const TextStyle(color: Colors.green),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Color _getStatusColor(TranslationStatus status) {
    switch (status) {
      case TranslationStatus.waiting:
        return Colors.grey;
      case TranslationStatus.processing:
        return Colors.blue;
      case TranslationStatus.completed:
        return Colors.green;
      case TranslationStatus.error:
        return Colors.red;
    }
  }

  IconData _getStatusIcon(TranslationStatus status) {
    switch (status) {
      case TranslationStatus.waiting:
        return Icons.schedule;
      case TranslationStatus.processing:
        return Icons.sync;
      case TranslationStatus.completed:
        return Icons.check_circle;
      case TranslationStatus.error:
        return Icons.error;
    }
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider.value(
      value: _translationState,
      child: Scaffold(
        appBar: AppBar(
          title: const Text('Translation Progress'),
          backgroundColor: Theme.of(context).colorScheme.inversePrimary,
          actions: [
            Consumer<TranslationState>(
              builder: (context, state, child) {
                if (state.allTranslationsComplete) {
                  return Padding(
                    padding: const EdgeInsets.only(right: 16),
                    child: ElevatedButton.icon(
                      onPressed: _downloadAllTranslations,
                      icon: const Icon(Icons.download),
                      label: const Text('Download All'),
                    ),
                  );
                }
                return const SizedBox.shrink();
              },
            ),
          ],
          bottom: PreferredSize(
            preferredSize: const Size.fromHeight(8),
            child: Consumer<TranslationState>(
              builder: (context, state, child) {
                return LinearProgressIndicator(
                  value: state.progress,
                  backgroundColor: Colors.grey.shade300,
                );
              },
            ),
          ),
        ),
        body: Column(
          children: [
            _buildTabBar(),
            Expanded(
              child: _buildTabBarView(),
            ),
          ],
        ),
      ),
    );
  }
}
