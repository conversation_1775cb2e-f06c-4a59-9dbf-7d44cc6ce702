import 'package:flutter/material.dart';
import '../models/translation_data.dart';
import '../widgets/language_selector.dart';
import '../widgets/file_upload_widget.dart';
import 'translation_screen.dart';

class ConfigurationScreen extends StatefulWidget {
  const ConfigurationScreen({super.key});

  @override
  State<ConfigurationScreen> createState() => _ConfigurationScreenState();
}

class _ConfigurationScreenState extends State<ConfigurationScreen> {
  final _formKey = GlobalKey<FormState>();
  final _apiKeyController = TextEditingController();
  
  String? _baseLocale;
  List<String> _targetLocales = [];
  Map<String, dynamic>? _baseJson;
  
  // Error messages
  String? _baseLocaleError;
  String? _targetLocalesError;
  String? _apiKeyError;
  String? _fileError;

  @override
  void dispose() {
    _apiKeyController.dispose();
    super.dispose();
  }

  void _onFileSelected(Map<String, dynamic> jsonData) {
    setState(() {
      _baseJson = jsonData;
      _fileError = null;
    });
  }

  void _onBaseLocaleChanged(String? locale) {
    setState(() {
      _baseLocale = locale;
      _baseLocaleError = null;
      
      // Remove base locale from target locales if selected
      if (locale != null && _targetLocales.contains(locale)) {
        _targetLocales.remove(locale);
      }
    });
  }

  void _onTargetLocalesChanged(List<String> locales) {
    setState(() {
      _targetLocales = locales;
      _targetLocalesError = null;
    });
  }

  bool _validateInputs() {
    bool isValid = true;
    
    // Validate base locale
    if (_baseLocale == null) {
      setState(() {
        _baseLocaleError = 'Please select a base language';
      });
      isValid = false;
    }
    
    // Validate target locales
    if (_targetLocales.isEmpty) {
      setState(() {
        _targetLocalesError = 'Please select at least one target language';
      });
      isValid = false;
    }
    
    // Validate API key
    if (_apiKeyController.text.trim().isEmpty) {
      setState(() {
        _apiKeyError = 'Please enter your OpenAI API key';
      });
      isValid = false;
    }
    
    // Validate file
    if (_baseJson == null) {
      setState(() {
        _fileError = 'Please upload a JSON localization file';
      });
      isValid = false;
    }
    
    return isValid;
  }

  void _startTranslation() {
    if (_validateInputs()) {
      final config = TranslationConfig(
        baseLocale: _baseLocale!,
        targetLocales: _targetLocales,
        apiKey: _apiKeyController.text.trim(),
        baseJson: _baseJson!,
      );
      
      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => TranslationScreen(config: config),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Bulk JSON Translator'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(24),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Configuration',
                style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'Configure your translation settings and upload your base localization file.',
                style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  color: Colors.grey.shade600,
                ),
              ),
              const SizedBox(height: 32),
              
              // Base Language Selector
              LanguageSelector(
                selectedLocale: _baseLocale,
                onChanged: _onBaseLocaleChanged,
                label: 'Base Language',
                errorMessage: _baseLocaleError,
              ),
              const SizedBox(height: 24),
              
              // Target Languages Selector
              MultiLanguageSelector(
                selectedLocales: _targetLocales,
                onChanged: _onTargetLocalesChanged,
                label: 'Target Languages',
                errorMessage: _targetLocalesError,
                excludeLocale: _baseLocale,
              ),
              const SizedBox(height: 24),
              
              // API Key Input
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'OpenAI API Key',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 8),
                  TextFormField(
                    controller: _apiKeyController,
                    decoration: InputDecoration(
                      hintText: 'Enter your OpenAI API key',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      errorBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide: const BorderSide(color: Colors.red, width: 2),
                      ),
                      prefixIcon: const Icon(Icons.key),
                      errorText: _apiKeyError,
                    ),
                    obscureText: true,
                    onChanged: (value) {
                      if (_apiKeyError != null) {
                        setState(() {
                          _apiKeyError = null;
                        });
                      }
                    },
                  ),
                ],
              ),
              const SizedBox(height: 24),
              
              // File Upload
              FileUploadWidget(
                onFileSelected: _onFileSelected,
                errorMessage: _fileError,
              ),
              const SizedBox(height: 32),
              
              // Start Translation Button
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: _startTranslation,
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: const Text(
                    'Start Translation',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
