import 'package:flutter/foundation.dart';
import 'translation_data.dart';

class TranslationState extends ChangeNotifier {
  TranslationConfig? _config;
  List<TranslationResult> _results = [];
  bool _isTranslating = false;
  int _currentTranslationIndex = 0;

  TranslationConfig? get config => _config;
  List<TranslationResult> get results => _results;
  bool get isTranslating => _isTranslating;
  int get currentTranslationIndex => _currentTranslationIndex;
  
  double get progress {
    if (_config == null || _config!.targetLocales.isEmpty) return 0.0;
    return _currentTranslationIndex / _config!.targetLocales.length;
  }

  bool get allTranslationsComplete {
    return _results.isNotEmpty &&
        _results.every((result) => 
            result.status == TranslationStatus.completed ||
            result.status == TranslationStatus.error);
  }

  void setConfig(TranslationConfig config) {
    _config = config;
    _results = config.targetLocales
        .map((locale) => TranslationResult(
              locale: locale,
              translatedJson: {},
              status: TranslationStatus.waiting,
            ))
        .toList();
    _currentTranslationIndex = 0;
    notifyListeners();
  }

  void startTranslation() {
    _isTranslating = true;
    _currentTranslationIndex = 0;
    notifyListeners();
  }

  void updateTranslationResult(int index, TranslationResult result) {
    if (index >= 0 && index < _results.length) {
      _results[index] = result;
      notifyListeners();
    }
  }

  void moveToNextTranslation() {
    if (_currentTranslationIndex < _results.length - 1) {
      _currentTranslationIndex++;
    } else {
      _isTranslating = false;
    }
    notifyListeners();
  }

  void retryTranslation(int index) {
    if (index >= 0 && index < _results.length) {
      _results[index] = _results[index].copyWith(
        status: TranslationStatus.waiting,
        error: null,
        translatedJson: {},
        missingKeys: [],
      );
      notifyListeners();
    }
  }

  void reset() {
    _config = null;
    _results = [];
    _isTranslating = false;
    _currentTranslationIndex = 0;
    notifyListeners();
  }
}
