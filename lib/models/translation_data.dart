enum TranslationStatus {
  waiting,
  processing,
  completed,
  error,
}

class LocaleInfo {
  final String code;
  final String name;
  final String flag;

  const LocaleInfo({
    required this.code,
    required this.name,
    required this.flag,
  });
}

class TranslationResult {
  final String locale;
  final Map<String, dynamic> translatedJson;
  final TranslationStatus status;
  final String? error;
  final List<String> missingKeys;

  TranslationResult({
    required this.locale,
    required this.translatedJson,
    required this.status,
    this.error,
    this.missingKeys = const [],
  });

  TranslationResult copyWith({
    String? locale,
    Map<String, dynamic>? translatedJson,
    TranslationStatus? status,
    String? error,
    List<String>? missingKeys,
  }) {
    return TranslationResult(
      locale: locale ?? this.locale,
      translatedJson: translatedJson ?? this.translatedJson,
      status: status ?? this.status,
      error: error ?? this.error,
      missingKeys: missingKeys ?? this.missingKeys,
    );
  }
}

class TranslationConfig {
  final String baseLocale;
  final List<String> targetLocales;
  final String apiKey;
  final Map<String, dynamic> baseJson;

  TranslationConfig({
    required this.baseLocale,
    required this.targetLocales,
    required this.api<PERSON>ey,
    required this.baseJson,
  });
}
